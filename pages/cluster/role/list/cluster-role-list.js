/**
 * @file pages/cluster/role/list/cluster-role-list.js
 * <AUTHOR> Console
 * @description ClusterRole 列表组件
 */

import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {Button, Pagination, Search, Tag, Tooltip, Notification, Dialog} from '@baidu/sui';
import {Table} from '@baidu/sui';
import {OutlinedRefresh} from '@baidu/sui-icon';
import YamlEditor from '../../../../components/yaml-editor';
import DeleteDialog from '../components/delete-dialog';
import {utcToTime} from '../../../../utils/util';
import jsyaml from 'js-yaml';
import './style.less';

const {asComponent, invokeAppComp, invokeBceSanUI} = decorators;

const template = html`
    <template>
        <div class="cluster-role-list">
            <div class="cluster-role-filter">
                <div class="create">
                    <s-button skin="primary" on-click="onCreate">创建</s-button>
                </div>
                <div class="refresh">
                    <s-button class="s-icon-button" on-click="onRefresh">
                        <s-icon-refresh is-button="{{false}}" />
                    </s-button>
                </div>
                <div class="name">
                    <s-search
                        value="{=searchValue=}"
                        on-search="onSearch"
                        placeholder="请输入角色名称进行搜索"
                        width="320px"
                    />
                </div>
            </div>

            <!-- 列表表格 -->
            <div class="cluster-role-list-table">
                <s-table columns="{{columns}}" datasource="{{datasource}}" loading="{{loading}}">
                    <!-- 名称列 -->
                    <div slot="c-name">
                        <a href="{{getDetailUrl(row)}}">{{row.metadata.name}}</a>
                        <s-tag s-if="row.isAdminRole" size="mini">管理员角色</s-tag>
                        <s-tag s-if="row.isSystemRole" size="mini">系统默认角色</s-tag>
                    </div>

                    <div slot="c-action" class="operation-wrapper">
                        <s-button
                            s-if="row.isAdminRole || row.isSystemRole"
                            skin="stringfy"
                            on-click="onViewYaml(row)"
                            >查看YAML</s-button
                        >
                        <s-button s-else skin="stringfy" on-click="onEditYaml(row)"
                            >编辑YAML</s-button
                        >

                        <s-tooltip s-if="row.isAdminRole" content="管理员角色不支持删除">
                            <s-button skin="stringfy" disabled>删除</s-button>
                        </s-tooltip>
                        <s-tooltip s-elif="row.isSystemRole" content="系统默认角色不支持删除">
                            <s-button skin="stringfy" disabled>删除</s-button>
                        </s-tooltip>
                        <s-button s-else skin="stringfy" on-click="onDelete(row)">删除</s-button>
                    </div>
                </s-table>
            </div>

            <!-- 分页 -->
            <s-pagination
                s-if="totalCount > 0"
                total="{{totalCount}}"
                pageSize="{{pageSize}}"
                pageSizes="{{[10, 20, 50, 100]}}"
                layout="pageSize,pager,go"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />

            <!-- YAML 弹框 -->
            <s-dialog
                class="yaml-dialog"
                open="{=yamlDialogOpen=}"
                confirming="{=confirming=}"
                title="{{yamlDialogTitle}}"
                width="1120"
                height="600"
                closeAfterMaskClick="{{false}}"
                on-confirm="onYamlConfirm"
                on-close="onYamlClose"
            >
                <yaml-editor
                    value="{=yamlContent=}"
                    height="{{450}}"
                    enhanced="{{true}}"
                    readonly="{{yamlDialogMode === 'view'}}"
                />
                <div slot="footer" s-if="yamlDialogMode === 'view'">
                    <s-button on-click="onYamlClose">关闭</s-button>
                </div>
                <div slot="footer" s-else>
                    <s-button on-click="onYamlClose">取消</s-button>
                    <s-button skin="primary" on-click="onYamlConfirm">确认</s-button>
                </div>
            </s-dialog>

            <!-- 删除确认弹框 -->
            <delete-dialog
                s-ref="deleteDialog"
                on-confirm="onDeleteConfirm"
                on-close="onDeleteClose"
            />
        </div>
    </template>
`;

@asComponent('@cluster-role-list')
@invokeAppComp
@invokeBceSanUI
export default class ClusterRoleList extends Component {
    static template = template;
    static components = {
        's-button': Button,
        's-table': Table,
        's-pagination': Pagination,
        's-search': Search,
        's-tag': Tag,
        's-tooltip': Tooltip,
        's-icon-refresh': OutlinedRefresh,
        's-dialog': Dialog,
        'yaml-editor': YamlEditor,
        'delete-dialog': DeleteDialog,
    };

    static filters = {
        formatTime: utcToTime,
    };

    // 管理员角色列表 - 由创建集群时 CCE 自动创建
    static ADMIN_ROLES = ['cce:admin'];

    // 判断是否为系统默认角色 - 带有 kubernetes.io/bootstrapping: rbac-defaults 标签
    static isSystemDefaultRole(yamlData) {
        const labels = yamlData?.metadata?.labels || {};
        return labels['kubernetes.io/bootstrapping'] === 'rbac-defaults';
    }

    // 判断是否为管理员角色
    static isAdminRole(yamlData) {
        const roleName = yamlData?.metadata?.name;
        return ClusterRoleList.ADMIN_ROLES.includes(roleName);
    }

    initData() {
        return {
            // 列表数据
            datasource: [],
            loading: false,
            totalCount: 0,
            pageSize: 20,
            pageNo: 1,
            searchValue: '',

            // 表格列配置
            columns: [
                {
                    name: 'name',
                    label: '角色名称',
                    width: 300,
                },
                {
                    name: 'createTime',
                    label: '创建时间',
                    width: 180,
                    render(item) {
                        try {
                            if (item?.metadata?.creationTimestamp) {
                                return utcToTime(item.metadata.creationTimestamp);
                            }
                            return '-';
                        } catch (error) {
                            return '-';
                        }
                    },
                },
                {
                    name: 'action',
                    label: '操作',
                    width: 160,
                    // fixed: 'right',
                },
            ],

            // 弹框状态
            yamlDialogOpen: false,
            yamlDialogMode: 'create', // create | edit | view
            yamlDialogTitle: '',
            yamlContent: '',
            currentEditRow: null,
            confirming: false,
        };
    }

    attached() {
        // 加载数据
        this.loadData();
    }

    // 获取详情页面链接
    getDetailUrl(row) {
        const clusterUuid = this.data.get('clusterUuid');
        const name = row?.metadata?.name;
        return `#/cce/cluster/role/detail?clusterUuid=${clusterUuid}&name=${name}&type=clusterrole`;
    }

    // 加载列表数据
    async loadData() {
        try {
            this.data.set('loading', true);
            const {clusterUuid, pageNo, pageSize, searchValue} = this.data.get();

            // 如果没有 clusterUuid，直接返回
            if (!clusterUuid) {
                return;
            }

            const params = {
                group: 'rbac.authorization.k8s.io',
                version: 'v1',
                kind: 'ClusterRole',
                namespace: '', // ClusterRole 不需要 namespace
                clusterUuid,
                pageNo,
                pageSize,
                name: searchValue?.trim(),
            };

            const response = await this.$http.getResourceObjList(params);

            const items = (response?.result?.items || []).map(item => {
                try {
                    const yamlData = typeof item === 'string' ? JSON.parse(item) : item;
                    // 创建包装对象，将 YAML 数据和 UI 字段分开
                    return {
                        // 原始 YAML 数据
                        yamlData: yamlData,
                        // UI 相关字段
                        isAdminRole: ClusterRoleList.isAdminRole(yamlData),
                        isSystemRole: ClusterRoleList.isSystemDefaultRole(yamlData),
                        // 为了兼容现有代码，保留对 metadata 的直接访问
                        metadata: yamlData?.metadata,
                        // 其他可能需要的字段
                        kind: yamlData?.kind,
                        apiVersion: yamlData?.apiVersion,
                    };
                } catch (error) {
                    return {
                        yamlData: {},
                        isAdminRole: false,
                        isSystemRole: false,
                        metadata: {},
                    };
                }
            });

            this.data.set('datasource', items);
            this.data.set('totalCount', response?.result?.listMeta?.totalItems || items.length);
        } catch (error) {
            Notification.error('加载列表失败: ' + (error.message || '未知错误'));
        } finally {
            this.data.set('loading', false);
        }
    }

    // 搜索
    onSearch() {
        this.data.set('pageNo', 1);
        this.loadData();
    }

    // 刷新
    onRefresh() {
        this.loadData();
    }

    // 分页变化
    onPageChange({value}) {
        this.data.set('pageNo', value.page);
        this.loadData();
    }

    onPageSizeChange({value}) {
        this.data.set('pageSize', value.pageSize);
        this.data.set('pageNo', 1);
        this.loadData();
    }

    // 创建
    onCreate() {
        const defaultYaml = `apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: clusterrole-example
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]`;

        this.data.set('yamlDialogOpen', true);
        this.data.set('yamlDialogMode', 'create');
        this.data.set('yamlDialogTitle', 'YAML 创建资源');
        this.data.set('yamlContent', defaultYaml);
        this.data.set('currentEditRow', null);
    }

    // 编辑 YAML（参考 resource-obj 的实现）
    onEditYaml(row) {
        this.data.set('yamlDialogOpen', true);
        this.data.set('yamlDialogMode', 'edit');
        this.data.set('yamlDialogTitle', '编辑 YAML');
        this.data.set('currentEditRow', row);
        try {
            // 使用原始 YAML 数据而不是包装对象
            this.data.set('yamlContent', jsyaml.safeDump(row.yamlData));
        } catch (error) {
            Notification.error('YAML 序列化失败');
        }
    }

    // 查看 YAML（参考 resource-obj 的实现）
    onViewYaml(row) {
        this.data.set('yamlDialogOpen', true);
        this.data.set('yamlDialogMode', 'view');
        this.data.set('yamlDialogTitle', '查看 YAML');
        this.data.set('currentEditRow', row);
        try {
            // 使用原始 YAML 数据而不是包装对象
            this.data.set('yamlContent', jsyaml.safeDump(row.yamlData));
        } catch (error) {
            Notification.error('YAML 序列化失败');
        }
    }

    // 删除
    onDelete(row) {
        const resourceName = row.metadata?.name;
        const createTime = utcToTime(row.metadata?.creationTimestamp);
        this.data.set('currentEditRow', row);
        // ClusterRole 没有 namespace，传递 null 并明确指定 resourceType
        this.ref('deleteDialog').show(resourceName, createTime, null, 'clusterrole');
    }

    // YAML 弹框确认
    async onYamlConfirm() {
        if (this.data.get('yamlDialogMode') === 'view') {
            // 查看模式下，确定按钮直接关闭弹窗
            this.onYamlClose();
            return;
        }

        try {
            this.data.set('confirming', true);
            const {yamlContent, yamlDialogMode, clusterUuid, currentEditRow} = this.data.get();
            const yamlData = jsyaml.load(yamlContent);

            if (yamlDialogMode === 'create') {
                await this.$http.createResource({
                    clusterUuid,
                    content: yamlContent,
                    namespace: yamlData?.metadata?.namespace || 'default',
                    validate: true,
                });
                Notification.success('创建成功');
            } else if (yamlDialogMode === 'edit') {
                const params = {
                    group: 'rbac.authorization.k8s.io',
                    version: 'v1',
                    kind: 'ClusterRole',
                    name: currentEditRow.metadata?.name,
                    clusterUuid,
                };
                await this.$http.editResource(params, yamlData);
                Notification.success('编辑成功');
            }

            this.onYamlClose();
            this.loadData();
        } catch (error) {
            Notification.error(error.message || '操作失败');
            this.data.set('confirming', false);
        }
    }

    // YAML 弹框关闭
    onYamlClose() {
        this.data.set('yamlDialogOpen', false);
        this.data.set('yamlContent', '');
        this.data.set('currentEditRow', null);
        this.data.set('confirming', false);
    }

    // 删除确认
    async onDeleteConfirm() {
        try {
            const {clusterUuid, currentEditRow} = this.data.get();
            const params = {
                kind: 'ClusterRole',
                clusterUuid,
                resourceList: [
                    {
                        kind: 'ClusterRole',
                        group: 'rbac.authorization.k8s.io',
                        version: 'v1',
                        name: currentEditRow.metadata?.name,
                    },
                ],
                method: 'delete',
            };

            await this.$http.deleteResource(params);
            this.ref('deleteDialog').data.set('confirming', false);
            this.ref('deleteDialog').data.set('open', false);
            this.loadData();
            Notification.success('删除成功');
        } catch (error) {
            Notification.error(error.message || '删除失败');
            this.ref('deleteDialog').data.set('confirming', false);
        }
    }

    // 删除弹框关闭
    onDeleteClose() {
        this.data.set('currentEditRow', null);
    }
}
