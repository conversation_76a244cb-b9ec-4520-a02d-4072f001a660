.cluster-role-list {
    // 参考事件中心的工具栏样式
    .mt10 {
        margin-top: 10px;
    }

    .mb10 {
        margin-bottom: 10px;
    }

    .float-right {
        float: right;

        .s-button {
            margin-left: 8px;
            padding: 0 7px;
        }

        .button-icon {
            display: inline-flex;
        }
    }

    // 过滤器工具栏样式
    .cluster-role-filter {
        height: 32px;
        margin: 10px 0;

        .create {
            float: left;
        }

        .name {
            float: right;
            margin-left: 8px;
        }

        .refresh {
            float: right;
            margin-left: 8px;
            width: 32px;

            .s-button {
                padding: 0 7px;
            }
        }
    }

    // 表格样式
    .cluster-role-list-table {
        margin-top: 16px;

        .s-table {
            .s-table-container {
                overflow-x: hidden;
            }

            .s-table-body {
                max-height: calc(100vh - 400px);
                overflow-y: auto;
                overflow-x: hidden;
            }
        }
    }

    .s-table {
        .system-role-tag {
            margin-left: 8px;
        }
        .s-table-cell-action {
            .s-button {
                padding: 0 4px;
            }
        }
    }

    .s-pagination {
        height: 32px;
        float: right;
        margin-top: 16px;
        text-align: right;
    }
}

// Role 列表样式
.role-list {
    // 参考事件中心的工具栏样式
    .mt10 {
        margin-top: 10px;
    }

    .mb10 {
        margin-bottom: 10px;
    }

    .float-right {
        float: right;

        .s-button {
            margin-left: 8px;
            padding: 0 7px;
        }

        .button-icon {
            display: inline-flex;
        }
    }

    .role-filter {
        height: 32px;
        margin: 10px 0;

        .create {
            float: left;
        }

        .namespace {
            float: right;
            margin-left: 8px;
            .s-select {
                margin-left: 6px;
                .s-selectdropdown > div:first-child {
                    overflow: hidden;
                    // max-height: 195px;
                }
            }
        }

        .name {
            float: right;
            margin-left: 8px;
        }

        .refresh {
            float: right;
            margin-left: 8px;
            width: 32px;
            .s-button {
                padding: 0 7px;
            }
        }
    }

    // 表格样式
    .role-list-table {
        margin-top: 16px;

        .s-table {
            .s-table-container {
                overflow-x: hidden;
            }

            .s-table-body {
                max-height: calc(100vh - 400px);
                overflow-y: auto;
                overflow-x: hidden;
            }
        }
    }

    .s-table {
        .system-role-tag {
            margin-left: 8px;
        }
        .s-table-cell-action {
            .s-button {
                padding: 0 4px;
            }
        }
    }

    .s-pagination {
        height: 32px;
        float: right;
        margin-top: 10px;
        text-align: right;
    }
}
